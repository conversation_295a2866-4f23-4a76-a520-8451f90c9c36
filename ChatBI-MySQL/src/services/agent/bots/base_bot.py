"""
Base bot class for all agent types.
"""
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import textwrap

from agents import Agent, Model
from src.services.agent.utils.model_provider import LITE_LLM_MODEL
from src.services.agent.utils.permissions import get_user_permission
from src.utils.logger import logger


class BaseBot(ABC):
    """
    Base class for all bot implementations.

    This abstract class defines the interface that all bot types must implement.
    Each bot type should handle its own:
    - System instructions
    - Permission definitions
    - Tool selection
    - Model selection
    """

    def __init__(self, user_info: Dict[str, Any]):
        """
        Initialize the bot with user information.

        Args:
            user_info: Dictionary containing user information
        """
        self.user_info = user_info
        self.user_name = user_info.get("name", "未知用户")
        self.job_title = user_info.get("job_title", "未知职位")

    @abstractmethod
    def get_description(self) -> str:
        """
        Get a description of the bot's capabilities.

        Returns:
            str: Description of the bot's capabilities
        """
        pass

    def get_user_realtime_instruction(self) -> str:
        """
        获取用户实时指令，结合当前时间、默认数据权限等信息，生成用于指导Agent行为的实时指令。

        Returns:
            str: 实时指令文本
        """
        try:
            date_time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            default_allowed_date = (datetime.now() - timedelta(days=31)).strftime(
                "%Y-%m-%d 00:00:00"
            )
            data_permission_markup = get_user_permission(self.user_info)
            # 生成实时指令，包含用户、时间、权限等关键信息
            realtime_instruction = textwrap.dedent(
                f"""
                请根据{self.user_name}的问题，使用工具查找相关表的DDL语句，然后参考DDL语句编写SQL来解答用户的问题。
                【非常重要，一定要遵守】用户{self.user_name}是重度中文用户，一点英语也不会，所以请始终用中文回答。
                当前时间: {date_time_of_now}
                当请求了orders表时，除非用户强烈要求，否则只需请求自{default_allowed_date}以来的数据。
                当请求了merchant表时，除非用户明确指明查询异常状态的门店（如被拉黑，已注销，等），则必须筛选`merchant`.`islock` = 0的门店。
                【非常重要，一定要遵守】当你完成了SQL的编写后，一定要使用工具来为用户执行所编写的SQL，并返回执行结果。
                【非常重要，一定要遵守】数据权限申明：{data_permission_markup}。千万不可让销售员A去查询其他销售员的客户数据，如果发现用户越权查询，请立即停止查询并向用户严厉警告说明情况。
            """
            ).strip()
            return realtime_instruction
        except Exception as e:
            logger.exception(f"生成实时指令时发生错误: {e}")
            return f"生成实时指令时发生错误: {str(e)}"

    def get_user_realtime_instruction_as_message_object(self) -> List[dict]:
        """
        获取用户实时指令，并将其包装为一个消息对象。

        Returns:
            dict: 包含实时指令的消息对象
        """
        realtime_instruction = self.get_user_realtime_instruction()
        return [
            {"role": "user", "content": realtime_instruction},
            {"role": "assistant", "content": "好的，我明白了。"},
        ]

    @abstractmethod
    def create_agent(self, model: Optional[Model] = LITE_LLM_MODEL) -> Agent:
        """
        Create and return an agent instance.

        Args:
            model: Optional model instance to use (defaults to LITE_LLM_MODEL if not provided)

        Returns:
            Agent: The initialized agent
        """
        pass
