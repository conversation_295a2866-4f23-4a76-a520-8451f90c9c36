"""
优化后的查询运行器 - 解决并发问题
"""
import asyncio
import json
import queue
import threading
import concurrent.futures
from datetime import datetime
from typing import List, Dict, Generator
import weakref

from agents import Runner, RunConfig, ModelSettings
from src.models.user_info_class import UserInfo
from src.services.agent.bots.master_controller_bot import MasterControllerBot
from src.services.agent.utils.formatter import format_event_message
from src.services.agent.utils.model_provider import LITE_LLM_MODEL
from src.services.chatbot.history_service import (
    save_assistant_message,
    get_conversation_history_as_input_list
)
from src.utils.logger import logger
from src.utils.user_utils import get_valid_user_email
from src.utils.system_monitor import start_system_monitoring, stop_system_monitoring, log_current_usage
from src.utils.image_utils import process_images_for_ai

# 全局线程池 - 避免频繁创建销毁线程
THREAD_POOL = concurrent.futures.ThreadPoolExecutor(
    max_workers=20,  # 可根据需要调整
    thread_name_prefix="agent_worker"
)

# 移除事件循环池，改为按需创建和销毁事件循环以避免资源泄漏

# 改进的后台任务队列
class BackgroundTaskManager:
    def __init__(self, max_workers=10):
        self.task_queue = queue.Queue()
        self.executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=max_workers,
            thread_name_prefix="background_worker"
        )
        self.active_tasks = weakref.WeakSet()
        self._start_workers()

    def _start_workers(self):
        """启动后台工作线程"""
        for _ in range(3):  # 启动3个工作线程
            future = self.executor.submit(self._worker)
            self.active_tasks.add(future)

    def _worker(self):
        """后台工作线程"""
        while True:
            try:
                task = self.task_queue.get(timeout=30)  # 30秒超时
                if task is None:
                    break

                # 处理任务
                self._process_task(task)

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Background worker error: {e}")
            finally:
                self.task_queue.task_done()

    def _process_task(self, task):
        """处理后台任务"""
        try:
            user_query = task["user_query"]
            user_info = task["user_info"]
            access_token = task["access_token"]
            conversation_id = task["conversation_id"]
            model_name = task.get("model_name")

            # 执行任务并消费结果
            for _ in run_agent_query(
                user_query, user_info, access_token,
                conversation_id, model_name
            ):
                pass

            logger.info(f"后台任务完成 (Convo ID: {conversation_id})")

        except Exception as e:
            logger.error(f"Background task processing error: {e}")

    def submit_task(self, task):
        """提交后台任务"""
        self.task_queue.put(task)

# 全局后台任务管理器
BACKGROUND_MANAGER = BackgroundTaskManager()


def run_agent_query(
        user_query: str,
        user_info: dict = {},
        access_token: str = None,
        conversation_id: str = None,
        images: list = None,
        model_name: str = None,
) -> Generator:
    """
    优化后的查询处理函数
    """
    message_queue = queue.Queue()

    user_name = user_info.get("name")
    email = get_valid_user_email(user_info)
    union_id = user_info.get("union_id")
    summerfarm_api_token = user_info.get("summerfarm_api_token")

    user_obj = UserInfo(
        user_name=user_name,
        email=email,
        access_token=access_token,
        union_id=union_id,
        summerfarm_api_token=summerfarm_api_token,
        conversation_id=conversation_id
    )

    max_retries = 3

    def async_worker():
        """优化后的异步工作函数 - 使用按需创建的事件循环"""
        loop = None
        try:
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # 运行协程
            loop.run_until_complete(process_stream())

        except Exception as e:
            logger.exception(f"异步工作线程出错: {e}")
            message_queue.put({"type": "error", "content": str(e)})
            message_queue.put({"type": "final_result", "data": None})
        finally:
            # 确保事件循环被正确关闭
            if loop and not loop.is_closed():
                try:
                    # 取消所有待处理的任务
                    pending = asyncio.all_tasks(loop)
                    for task in pending:
                        task.cancel()

                    # 等待任务取消完成
                    if pending:
                        loop.run_until_complete(
                            asyncio.gather(*pending, return_exceptions=True)
                        )

                    loop.close()
                    logger.debug("Event loop closed properly")
                except Exception as e:
                    logger.error(f"Error closing event loop: {e}")

            message_queue.put(None)  # 结束标记

    async def process_stream():
        """处理流事件的协程"""
        try:
            final_input_list = None

            # 获取历史
            history_for_agent = []
            if conversation_id:
                history_for_agent = get_conversation_history_as_input_list(
                    user_name, email, conversation_id
                )
                logger.info(f"获取到 {len(history_for_agent)} 条历史消息")

            # 构建用户消息，支持多模态
            if images and len(images) > 0:
                logger.info(f"开始处理 {len(images)} 张图片，下载并转换为base64编码")

                # 下载图片并转换为base64编码
                processed_images = process_images_for_ai(images)

                if processed_images:
                    # 使用正确的多模态消息格式
                    content_parts = [{"type": "input_text", "text": user_query}]

                    # 添加处理后的图片内容
                    for base64_image in processed_images:
                        content_parts.append({
                            "type": "input_image",
                            "image_url": base64_image,
                            "detail": "auto"
                        })

                    user_message = {
                        "role": "user",
                        "content": content_parts
                    }
                    logger.info(f"用户消息包含 {len(processed_images)} 张已处理的图片，使用多模态格式")
                else:
                    # 如果所有图片都处理失败，回退到纯文本消息
                    logger.warning("所有图片处理失败，回退到纯文本消息")
                    user_message = {"role": "user", "content": user_query}
            else:
                # 纯文本消息
                user_message = {"role": "user", "content": user_query}

            messages = history_for_agent + [user_message]

            for attempt in range(1, max_retries + 1):
                bot = MasterControllerBot(user_info)
                agent = bot.create_agent(LITE_LLM_MODEL)

                total_messages = bot.get_user_realtime_instruction_as_message_object() + messages

                logger.info(f"第 {attempt} 次尝试处理查询")

                # 启动流式推理
                result = Runner.run_streamed(
                    agent,
                    input=total_messages,
                    max_turns=20,
                    run_config=RunConfig(
                        model_settings=ModelSettings(temperature=0.1)
                    ),
                    context=user_obj,
                )

                # 处理流事件
                collected_logs = ""
                try:
                    async for event in result.stream_events():
                        message = format_event_message(event)
                        if message:
                            msg_type = message.get("type")
                            if msg_type in ["log", "handoff_log", "tool_output"] and message.get("content"):
                                collected_logs += str(message.get("content")) + "\n"
                            message_queue.put(message)

                except Exception as e:
                    logger.exception(f"流事件处理出错: {e}")
                    if "Event loop is closed" in str(e):
                        message_queue.put({"type": "error", "content": "事件循环已关闭"})
                        break
                    raise

                # 检测 handoff
                if "Handoff from " in collected_logs:
                    logger.info(f"第 {attempt} 次尝试成功")
                    final_input_list = result.to_input_list()
                    break

                if attempt < max_retries:
                    message_queue.put({"type": "info", "content": "🤖发生了一点小故障，重试中..."})

            message_queue.put({"type": "final_result", "data": final_input_list})

        except Exception as e:
            logger.exception(f"处理流时出错: {e}")
            message_queue.put({"type": "error", "content": str(e)})
            message_queue.put({"type": "final_result", "data": None})

    # 使用线程池执行异步工作
    future = THREAD_POOL.submit(async_worker)

    # 生成器函数
    def generate_and_save():
        full_assistant_response = ""
        full_assistant_logs = ""
        assistant_timestamp = None
        final_input_list_from_worker = None
        interrupted = False

        try:
            while True:
                try:
                    # 设置超时避免无限等待
                    message = message_queue.get(timeout=600)
                except queue.Empty:
                    logger.warning("消息队列获取超时")
                    # 通知用户超时情况
                    yield "[data]:" + json.dumps({"type": "error", "content": "响应超时，请稍后重试"}, ensure_ascii=False) + "\n"
                    break

                if message is None:
                    # 处理最终结果和保存逻辑
                    _handle_final_result(
                        final_input_list_from_worker,
                        full_assistant_response,
                        full_assistant_logs,
                        assistant_timestamp,
                        user_name, email, conversation_id
                    )
                    break

                if isinstance(message, dict):
                    msg_type = message.get("type")

                    if msg_type == "final_result":
                        final_input_list_from_worker = message.get("data")
                        continue

                    if not assistant_timestamp and msg_type != "error":
                        assistant_timestamp = int(datetime.now().timestamp() * 1000)

                    # 累积内容
                    if msg_type == "data":
                        full_assistant_response += message.get("content", "")
                    elif msg_type not in ["data", "final_result"] and message.get("content"):
                        log_line = f"[{msg_type.upper()}] {message.get('content', '')}"
                        full_assistant_logs += log_line + "\n"

                    # 发送到客户端
                    yield "[data]:" + json.dumps(message, ensure_ascii=False) + "\n"

        except (GeneratorExit, ConnectionResetError, BrokenPipeError) as e:
            interrupted = True
            logger.warning(f"客户端断开连接: {e}")
        except Exception as e:
            logger.exception(f"生成器错误: {e}")
        finally:
            if interrupted:
                # 提交后台任务
                BACKGROUND_MANAGER.submit_task({
                    "user_query": user_query,
                    "user_info": user_info,
                    "access_token": access_token,
                    "conversation_id": conversation_id,
                    "user_timestamp": assistant_timestamp,
                    "model_name": model_name,
                })
                logger.info(f"任务已提交到后台队列 (Convo ID: {conversation_id})")

    def _handle_final_result(final_input_list, response, logs, timestamp,
                           user_name, email, conversation_id):
        """处理最终结果和保存"""
        if not (response or logs):
            return

        try:
            structured_message = None
            if final_input_list and isinstance(final_input_list, list) and len(final_input_list) > 0:
                if (final_input_list and
                    isinstance(final_input_list[-1], dict) and
                    final_input_list[-1].get("role") == "assistant"):
                    structured_message = final_input_list[-1]

            output_as_input_json = None
            if structured_message:
                try:
                    output_as_input_json = json.dumps(
                        structured_message,
                        ensure_ascii=False,
                        indent=2
                    )
                except Exception as e:
                    logger.error(f"序列化结构化消息失败: {e}")

            if conversation_id and timestamp:
                save_assistant_message(
                    username=user_name,
                    email=email,
                    conversation_id=conversation_id,
                    content=response,
                    timestamp=timestamp,
                    logs=logs if logs else None,
                    output_as_input=output_as_input_json
                )
                logger.debug(f"助手回复已保存 (Convo ID: {conversation_id})")

        except Exception as e:
            logger.error(f"保存助手消息时出错: {e}")

    return generate_and_save()


# 优雅关闭函数
def shutdown_gracefully():
    """优雅关闭所有资源"""
    logger.info("开始优雅关闭...")

    # 记录关闭前的资源使用情况
    log_current_usage()

    # 关闭线程池
    THREAD_POOL.shutdown(wait=True)

    # 关闭后台任务管理器
    BACKGROUND_MANAGER.task_queue.put(None)
    BACKGROUND_MANAGER.executor.shutdown(wait=True)

    # 停止系统监控
    stop_system_monitoring()

    # 记录关闭后的资源使用情况
    log_current_usage()

    logger.info("优雅关闭完成")

# 启动系统监控
start_system_monitoring()

# 在应用退出时调用
import atexit
atexit.register(shutdown_gracefully)